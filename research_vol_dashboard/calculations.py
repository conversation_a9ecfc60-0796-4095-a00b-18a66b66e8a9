import logging
import math
import time
from datetime import datetime

import matplotlib.colors as mcolors
import numpy as np
import pandas as pd
import plotly.graph_objects as go
import seaborn as sns
import utils_general
from scipy.stats import norm

from .constants import (
    FILTER_MAPPING,
    QN_SUFFIX_MAPPING,
    SINGLE_TENOR_COLORS,
    WING_SPREAD_SIGNAL_THRESHOLD,
)
from .sandbox_plots_utils import (
    add_trace,
    create_bar_figure,
    create_stacked_area_figure,
)
from .typings import (
    AggregateInstrumentsChart,
    AggregationLevels,
    FlexTimeSeriesTarget,
)
from .utils import (
    convert_expiry_to_datetime,
    generate_date_range,
    is_lambda_context,
    round_down,
    round_up,
    save_image,
)
from .utils_plot import (
    generate_tickvals_and_text_for_aggregation_plots,
    get_y_scale,
)


def wing_spread_calc(
    data_df: pd.DataFrame, chart_metadata: dict[str, list[str]]
) -> tuple[pd.DataFrame, list[FlexTimeSeriesTarget]]:
    """
    Compute wing spread time series data from input DataFrame and chart metadata.

    Args:
        data_df: DataFrame containing volatility data with qualified names
        chart_metadata: Dictionary mapping qualified names to column pairs [wing_vol_col, atm_vol_col]

    Returns:
        DataFrame with wing spread calculations indexed by timestamp
    """
    st = time.time()

    smoothed_df = pd.DataFrame()

    for qn in chart_metadata.keys():
        wing_vol = (
            data_df[data_df["qualified_name"].str.contains(qn)][
                chart_metadata[qn][0]
            ]
            .resample("h")
            .first()
            .ffill()
        )
        atm_vol = (
            data_df[data_df["qualified_name"].str.contains(qn)][
                chart_metadata[qn][1]
            ]
            .resample("h")
            .first()
            .ffill()
        )

        wing_spread = (
            (wing_vol - atm_vol).rolling(4).mean()
            - (wing_vol - atm_vol).rolling(24 * 15).mean()
        ) - WING_SPREAD_SIGNAL_THRESHOLD[qn.split(".")[4]]

        wing_spread_df = pd.DataFrame(wing_spread)
        wing_spread_df["qualified_name"] = ".".join(
            [*qn.split(".")[:6], "wing_spread"]
        )

        wing_spread_df.columns = pd.Index([qn.split(".")[4], "qualified_name"])
        wing_spread_df = wing_spread_df.dropna()
        smoothed_df = pd.concat([smoothed_df, wing_spread_df], axis=0)

    smoothed_df.index.name = "timestamp"

    plot_targets: list[FlexTimeSeriesTarget] = [
        FlexTimeSeriesTarget(
            qualified_name=smoothed_df[["qualified_name", target]]
            .dropna()["qualified_name"]
            .unique()[0],
            target=target,
            trace_title=target,
            color=SINGLE_TENOR_COLORS.get(int(target.replace("d", ""))),
        )
        for target in [
            col for col in smoothed_df.columns if col != "qualified_name"
        ]
    ]

    logging.info(f"Computed wing spread in {time.time() - st}s")

    return smoothed_df, plot_targets


def compute_btc_eth(data_df: pd.DataFrame, chart_metadata):
    logging.info("COMPUTING BTC/ETH TIME SERIES")
    series_dict = {}

    for qn, target in chart_metadata.items():
        series_dict[qn] = data_df[data_df["qualified_name"].str.contains(qn)][
            target
        ]

    data = list(series_dict.values())
    return data[0] / data[1]  # essentially, btc / eth


def compute_fr_option_premium(data_df: pd.DataFrame, chart_metadata):
    logging.info("COMPUTING FUNDING RATE / OPTION PREMIUM TIME SERIES")
    series_dict = {}

    for qn, target in chart_metadata.items():
        series_dict[qn] = data_df[data_df["qualified_name"].str.contains(qn)][
            target
        ]

    data = list(series_dict.values())
    fr, vols, futures, spot = remove_missing_rows(
        data[0], data[1], data[2], data[3]
    )
    p = 0.5

    numerator = ((fr * 100 * 3 * 7) * spot) * p
    denominator = black_76_call(futures, vols)

    return (
        (numerator / denominator)
        .resample("8h", offset=pd.Timedelta(0, "h"))
        .first()
    )


def black_76_call(
    F: float, sigma: float, r: float = 0.0, T: float = 1 / 52
) -> float:
    K = F
    d1 = (math.log(1) + 0.5 * sigma**2 * T) / (sigma * math.sqrt(T))
    d2 = d1 - sigma * math.sqrt(T)

    N_d1 = norm.cdf(d1)
    N_d2 = norm.cdf(d2)

    call_price = math.exp(-r * T) * (F * N_d1 - K * N_d2)

    return call_price


def remove_missing_rows(
    fr: "pd.Series[float]",
    vols: "pd.Series[float]",
    futures: "pd.Series[float]",
    spot: "pd.Series[float]",
) -> tuple[
    "pd.Series[float]",
    "pd.Series[float]",
    "pd.Series[float]",
    "pd.Series[float]",
]:
    fr = fr.drop(fr.index.difference(vols.index))
    fr = fr.drop(fr.index.difference(futures.index))

    vols = vols.drop(vols.index.difference(fr.index))
    vols = vols.drop(vols.index.difference(futures.index))

    futures = futures.drop(futures.index.difference(fr.index))
    futures = futures.drop(futures.index.difference(vols.index))

    spot = spot.drop(spot.index.difference(fr.index))
    spot = spot.drop(spot.index.difference(vols.index))

    return fr, vols, futures, spot


def instrument_aggregate_plotter(
    data: pd.DataFrame,
    chart_items: AggregateInstrumentsChart,
    chart_name: str,
    start: str,
    end: str,
) -> go.Figure:
    dollar_denomination = chart_items["chart_formats"]["dollar_denomination"]
    y_column = QN_SUFFIX_MAPPING[chart_items["target_suffix"]].split(".")[
        -1
    ]  # sum of oi
    # y_column = 'amt'
    df = _apply_filters(df=data, chart_items=chart_items, y_column=y_column)

    assert (
        len(
            df.loc[
                ~(df["qualified_name"].str.contains("spot")), "contract_type"
            ].unique()
        )
        == 1
    ), "More than one Contract type found!. investigate"
    contract_type = df.loc[
        ~(df["qualified_name"].str.contains("spot")), "contract_type"
    ].unique()[0]
    if dollar_denomination:
        # todo: move to more appropriate location
        if not contract_type == "linear":
            logging.warning(
                "Contract are not not linear, skipping price conversion"
            )

        df = _normalize_prices(
            df=df,
            y_col=y_column,
            dollar_denomination=dollar_denomination,
            contract_type=contract_type,
        )
        y_column = y_column + "_dollars"
    else:
        if contract_type == "inverse":
            logging.warning("Contract are inverse, conveting to baseAsset")
        df = _normalize_prices(
            df=df,
            y_col=y_column,
            dollar_denomination=dollar_denomination,
            contract_type=contract_type,
        )

    df = _aggregate_data(
        df=df,
        aggregation_level=chart_items["aggregation_level"],
        sub_aggregation_level=chart_items.get("sub_aggregation_level"),
        target_suffix=chart_items["target_suffix"],
        resample_frequency=chart_items["resample_frequency"],
        y_column=y_column,
    )

    # Verify aggregated data
    fig = _generate_plot(
        chart_items=chart_items,
        aggregated_data=df,
        aggregation_level=chart_items["aggregation_level"],
        sub_aggregation_level=chart_items.get("sub_aggregation_level"),
        target_suffix=chart_items["target_suffix"],
        y_column=y_column,
        resample_frequency=chart_items["resample_frequency"],
    )

    full_fig = fig.full_figure_for_development()
    range = full_fig.layout.yaxis.range
    diff = range[1] - range[0]
    tickvals, ticktext = generate_tickvals_and_text_for_aggregation_plots(
        min_val=round_down(range[0], get_y_scale(diff)),
        max_val=round_up(range[1], get_y_scale(diff)),
    )

    fig.update_layout(
        title={
            "y": 0.97,
            "x": 0.082,
            "xanchor": "center",
            "yanchor": "top",
            "font": {
                # "family": "Arial, sans-serif",  # Specify the font family here
                "size": 30,  # Specify the font size here
                "color": "white",  # Specify the font color here
            },
        },
        height=650,
        width=1450,
        font={"size": 20, "color": "white"},
        margin={"l": 150, "r": 30, "b": 0, "t": 0, "pad": 4},
        yaxis={
            "tickmode": "array",
            "tickprefix": "$" if dollar_denomination else None,
            "tickvals": tickvals,
            "ticktext": ticktext,
            # "ticktext": custom_y_ticktext,
            # "tickformat":'~g'
        },
        plot_bgcolor="#101A2E",
        paper_bgcolor="#101A2E",
        legend={
            "orientation": "h",
            "yanchor": "top",
            "y": 1.1,
            "xanchor": "center",
            "x": 0.5,
        },
    )
    fig.update_yaxes(
        showgrid=True, gridwidth=1, gridcolor="#293e68", zeroline=True
    )
    fig.update_xaxes(
        showgrid=True, gridwidth=1, gridcolor="#293e68", zeroline=False
    )

    filename = f"{chart_name}.png"
    if not is_lambda_context():
        save_image(file_name=filename, figure=fig)
    return fig


def _generate_plot(
    chart_items: AggregateInstrumentsChart,
    aggregated_data: pd.DataFrame,
    aggregation_level: AggregationLevels,
    target_suffix: str,
    y_column: str,
    resample_frequency: str,
    sub_aggregation_level: AggregationLevels | None = None,
) -> go.Figure:
    chart_type = chart_items["chart_formats"]["chart_type"]
    invert_aggregation_levels: list[str] = chart_items["chart_formats"].get(
        "invert_aggregation_levels", []
    )
    pandas_resample_freq = utils_general.INTERVAL_TO_PANDAS_ALIAS[
        resample_frequency
    ]
    show_full_legend = chart_items["chart_formats"].get(
        "show_full_legend", True
    )

    title = f"{target_suffix}"
    title = ""

    total_per_level = aggregated_data.groupby(aggregation_level)[y_column].sum()
    sorted_levels = total_per_level.sort_values(ascending=False).index

    fig = (
        create_bar_figure(title=title)
        if chart_type == "bar"
        else create_stacked_area_figure(title=title)
    )

    if aggregation_level == "option_type":
        color_mapping = {
            "C": "#1F51FF",
            "P": "#FF3131",
        }  # Blue for Calls, Red for Puts
        distinct_colors = [color_mapping[level] for level in sorted_levels]

    elif aggregation_level == "instrument":
        distinct_colors = []
        for level in sorted_levels:
            if "BTC" in level:
                distinct_colors.append("#1F51FF")
            elif "ETH" in level:
                distinct_colors.append("#FF3131")

    elif aggregation_level == "currency":
        color_mapping = {
            "BTC": "#FFCD00",
            "ETH": "#883CFF",
            "SOL": "#14F195",
            "XRP": "#008DFF",
            "CRV": "#FFC605",
            "TON": "#0098E9",
            "ADA": "#FFFFFF",
            "ATOM": "#D04EDC",
            "DOGE": "#B79C32",
        }
        distinct_colors = [color_mapping[level] for level in sorted_levels]

    else:
        distinct_colors = sns.color_palette("husl", len(sorted_levels)).as_hex()

    for idx, key in enumerate(sorted_levels):
        df_subset = aggregated_data[aggregated_data[aggregation_level] == key]

        if not sub_aggregation_level and "oi" in y_column:
            df_subset = _ffill(
                df=df_subset, freq=pandas_resample_freq, key=key, y_col=y_column
            )

        if sub_aggregation_level:
            sub_level_sums = df_subset.groupby(sub_aggregation_level)[
                y_column
            ].sum()
            sub_levels_sorted_by_sum = sub_level_sums.sort_values(
                ascending=False
            ).index

            sub_levels_sorted_by_sum = sorted(
                sub_levels_sorted_by_sum,
                key=lambda x: sort_key_function(x, sub_aggregation_level),
            )

            max_lightness = 0.5
            base_color = sns.color_palette([distinct_colors[idx]], desat=1.0)
            gradient = [
                np.array(base_color) * (1 - t) + np.array([1, 1, 1]) * t
                for t in np.linspace(
                    0, max_lightness, len(sub_levels_sorted_by_sum)
                )
            ]
            hex_colors = [mcolors.rgb2hex(color) for color in gradient]

            for sub_level, color in zip(
                sub_levels_sorted_by_sum, hex_colors, strict=False
            ):
                sub_df_subset = df_subset[
                    df_subset[sub_aggregation_level] == sub_level
                ]
                if "oi" in y_column:
                    sub_df_subset = _ffill(
                        df=sub_df_subset,
                        freq=pandas_resample_freq,
                        key=key + "_" + sub_level,
                        y_col=y_column,
                    )

                x_values = sub_df_subset["date"]
                y_values = sub_df_subset[y_column]
                name = f"{key} - {sub_level}" if show_full_legend else key

                inverted = key in invert_aggregation_levels
                show_legend = show_full_legend or (
                    not show_full_legend
                    and sub_level == sub_levels_sorted_by_sum[-1]
                )
                show_legend = False

                add_trace(
                    fig=fig,
                    x_values=x_values,
                    y_values=y_values,
                    name=name,
                    color=color,
                    inverted=inverted,
                    show_legend=show_legend,
                    chart_type=chart_type,
                )

        else:
            x_values = df_subset["date"]
            y_values = df_subset[y_column]

            inverted = key in invert_aggregation_levels

            add_trace(
                fig=fig,
                x_values=x_values,
                y_values=y_values,
                name=key,
                color=distinct_colors[idx],
                inverted=inverted,
                chart_type=chart_type,
                show_legend=False,
            )

    # if not show_full_legend and sub_aggregation_level:
    #     for idx, key in enumerate(sorted_levels):
    #         color = distinct_colors[idx]
    #         fig.add_shape(
    #             type="rect",
    #             x0=1.02,
    #             y0=1 - idx * 0.05,
    #             x1=1.04,
    #             y1=1.02 - idx * 0.05,
    #             xref="paper",
    #             yref="paper",
    #             line={"color": color},
    #             fillcolor=color,
    #         )
    #         fig.add_annotation(
    #             x=1.05,
    #             y=1 - idx * 0.05,
    #             xref="paper",
    #             yref="paper",
    #             text=key,
    #             showarrow=False,
    #             align="left",
    #         )
    return fig


def _apply_filters(
    df: pd.DataFrame, chart_items: AggregateInstrumentsChart, y_column: str
) -> pd.DataFrame:
    """
    This is a crutial point of the whole aggregation process.
    The AND and OR combination of condition play a significant role is what is returned

    """

    filters = chart_items["filters"]
    _is_correct_asset_type = df["asset_type"] == chart_items["asset_type"]
    _has_matching_suffix = ~(pd.isna(df[y_column]))
    _is_spot_qn = df["qualified_name"].str.contains("spot")

    # Loop through the filters and apply them dynamically
    accumulated_filter = pd.Series([True] * len(df), index=df.index)
    for filter_key, column_name in FILTER_MAPPING.items():
        if filter_key in filters:
            if filter_key == "exchanges":
                filter_values = [_ef["exchange"] for _ef in filters[filter_key]]
            else:
                filter_values = filters[filter_key]

            current_filter = df[column_name].isin(filter_values)
            current_filter = current_filter.reindex(df.index, fill_value=False)
            accumulated_filter &= current_filter

    combined_condition = _is_spot_qn | (
        _is_correct_asset_type & _has_matching_suffix & accumulated_filter
    )
    df = df[combined_condition]

    return df


def _aggregate_data(
    df: pd.DataFrame,
    aggregation_level: str,
    target_suffix: str,
    resample_frequency: str,
    y_column: str,
    sub_aggregation_level: str | None = None,
) -> pd.DataFrame:
    df.sort_index(inplace=True)
    pandas_resample_freq = utils_general.INTERVAL_TO_PANDAS_ALIAS[
        resample_frequency
    ]
    # aggregation_columns = [
    #     pd.Grouper(freq=pandas_resample_freq),
    #     aggregation_level,
    # ]
    aggregation_columns = [aggregation_level]
    if sub_aggregation_level:
        aggregation_columns.append(sub_aggregation_level)

    if target_suffix == "volume":
        aggregated_data = (
            df.groupby(aggregation_columns)[y_column]
            .resample(pandas_resample_freq)
            .sum()
            .reset_index()
        )
    elif target_suffix == "open_interest":
        # lowest level of data available. All else and we will need to sum
        if aggregation_level != "instrument":
            sum_aggregation = (
                df.groupby(aggregation_columns)[y_column]
                .resample(pandas_resample_freq)
                .sum()
                .reset_index()
            )
            sum_aggregation.set_index("date", inplace=True)
        else:
            assert (
                not sub_aggregation_level
            ), "Cannot specify instruments as an aggregation level as well as a sub aggregation level"
            sum_aggregation = df

        # Resample to the specified frequency and take the last observed occurrence
        resampled_data = []
        for name, group in sum_aggregation.groupby(
            [aggregation_level, sub_aggregation_level]
            if sub_aggregation_level
            else aggregation_level
        ):
            resampled = group.resample(pandas_resample_freq).last()
            if sub_aggregation_level:
                resampled[[aggregation_level, sub_aggregation_level]] = name
            else:
                resampled[aggregation_level] = name

            resampled_data.append(resampled)

        aggregated_data = pd.concat(resampled_data).reset_index()
    else:
        raise NotImplementedError(f"Unknown data type {target_suffix}")

    return aggregated_data


def _normalize_prices(
    df: pd.DataFrame, y_col: str, contract_type: str, dollar_denomination: bool
) -> pd.DataFrame:
    # todo: consider contract type

    # todo: move all logic to prep data function
    spot_df = df[df["qualified_name"].str.contains(".spot.")].copy()
    non_spot_df = df[~(df["qualified_name"].str.contains(".spot."))].copy()
    spot_df.loc[:, "currency"] = spot_df["qualified_name"].str.extract(
        r"\.(\w+)_USD\.", expand=False
    )
    spot_df = spot_df[["timestamp", "currency", "px"]].rename(
        columns={"px": "px_spot"}
    )

    # Generate sets of indices
    instrument_index_set = set(non_spot_df.index)
    spot_index_set = set(spot_df.index)

    # Find missing indices in perps_df that are present in spot_df
    missing_in_instrument = spot_index_set - instrument_index_set

    # Find missing indices in spot_df that are present in perps_df
    missing_in_spot = instrument_index_set - spot_index_set

    assert not missing_in_spot, "Missing spot timestamps"
    if missing_in_instrument:
        logging.warning(
            f"Missing Instrument Data for timestamps {sorted(utils_general.to_iso(dt) for dt in missing_in_instrument)}"
        )

    non_spot_df["timestamp"] = non_spot_df["timestamp"].astype(int)
    spot_df["timestamp"] = spot_df["timestamp"].astype(int)
    non_spot_df["currency"] = non_spot_df["currency"].astype(str)
    spot_df["currency"] = spot_df["currency"].astype(str)

    # Perform a left join to ensure all rows in perps_df are included
    merged_df = pd.merge(
        non_spot_df,
        spot_df,
        on=["timestamp", "currency"],
        how="left",
    )
    assert len(merged_df) == len(non_spot_df)

    if dollar_denomination:
        if contract_type == "linear":
            # volume is in baseAsset, normalize by contractSize * spot
            # todo: pay attention to changes here.
            if y_col == "oi":
                merged_df[f"{y_col}_dollars"] = (
                    merged_df[y_col]
                    * merged_df["px_spot"]
                    * merged_df["contractSize"]
                )
            elif y_col == "sum":
                merged_df[f"{y_col}_dollars"] = (
                    merged_df[y_col]
                    * merged_df["px_spot"]
                    * merged_df["contractSize"]
                )
        elif contract_type == "inverse":
            # volume is already in dollars, normalize by contractSize
            if y_col == "oi":
                merged_df[f"{y_col}_dollars"] = (
                    merged_df[y_col] * merged_df["contractSize"]
                )
            elif y_col == "sum":
                merged_df[f"{y_col}_dollars"] = (
                    merged_df[y_col] * merged_df["contractSize"]
                )
        else:
            raise NotImplementedError(
                "Invalid Contract Type when trying normalize prices"
            )

    else:
        if contract_type == "linear":
            # volume is in baseAsset, normalize by contractSize
            # todo: pay attention to changes here.
            if y_col == "oi":
                merged_df[y_col] = merged_df[y_col] * merged_df["contractSize"]
            elif y_col == "sum":
                merged_df[y_col] = merged_df[y_col] * merged_df["contractSize"]
        elif contract_type == "inverse":
            # in dollars - convert back to baseAsset
            if y_col == "sum":
                merged_df[y_col] = (
                    merged_df[y_col] * merged_df["contractSize"]
                ) / merged_df["px_spot"]
            elif y_col == "oi":
                merged_df[y_col] = (
                    merged_df[y_col] * merged_df["contractSize"]
                ) / merged_df["px_spot"]

        else:
            raise NotImplementedError(
                "Invalid Contract Type when trying normalize prices"
            )

    merged_df["date"] = pd.to_datetime(merged_df["timestamp"], unit="ns")
    merged_df.set_index("date", inplace=True)

    assert len(merged_df) == len(non_spot_df)

    return merged_df


def sort_key_function(
    value: str | float, level_type: AggregationLevels
) -> datetime | float:
    if level_type == "expiry":
        assert isinstance(value, str)
        return convert_expiry_to_datetime(value)
    elif level_type == "strike":
        return float(value)
    else:
        raise ValueError(
            f"Unsupported sub-aggregation level type: {level_type}"
        )


CALCULATION_FUNCTIONS_DICT = {
    "BTC-ETH_calculation": compute_btc_eth,
    "FR-OP_calculation": compute_fr_option_premium,
    "WING_SPREAD_CALC": wing_spread_calc,
}


def _ffill(df: pd.DataFrame, freq: str, key: str, y_col: str) -> pd.DataFrame:
    df_subset = df.copy()
    df.set_index("date", inplace=True)

    start_date = df.index.min()
    end_date = df.index.max()

    # Create a complete index within the range of the existing DataFrame
    complete_index_within_range = generate_date_range(
        start_date=start_date, end_date=end_date, freq=freq
    )
    # Identify missing timestamps within this range
    missing_timestamps = find_missing_timestamps(
        df, complete_index_within_range
    )
    if not missing_timestamps.empty:
        logging.warning(
            f"Forward filling missing data, {missing_timestamps} for {key}"
        )

        # Reindex the DataFrame to the complete date range, introducing NaNs for missing entries
        df_reindexed = df.reindex(complete_index_within_range)

        # Forward fill the missing values
        df_subset = df_reindexed.ffill()
        df_subset.reset_index(inplace=True)
        df_subset.rename(columns={"index": "date"}, inplace=True)

    mask = df_subset[y_col] == 0

    # Log the indices where 0s are found
    if mask.any():
        logging.warning(
            f"0's found in the data, timestamps {df_subset['date'][mask].tolist()}, {key}"
        )

    # Replace 0 values with NaN in the specified column
    df_subset[y_col] = df_subset[y_col].mask(mask, np.nan)

    # Forward fill NaN values in the specified column
    df_subset[y_col].ffill(inplace=True)

    return df_subset


def find_missing_timestamps(
    df: pd.DataFrame, complete_index: pd.DatetimeIndex
) -> pd.DatetimeIndex:
    """Finds missing timestamps in the DataFrame compared to the complete index."""
    return complete_index.difference(df.index)
