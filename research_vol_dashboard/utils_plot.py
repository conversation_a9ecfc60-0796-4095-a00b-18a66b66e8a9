import base64
import math
from collections.abc import Sequence
from datetime import UTC, datetime
from pathlib import Path
from typing import Literal

import matplotlib.colors as mc
import numpy as np
import pandas as pd
import plotly.graph_objects as go
import seaborn as sns
from numpy._typing import NDArray

from .typings import Tick


def bs_standard_timeseries_layout(
    figure: go.Figure,
    end_time: int,
    range_slider: bool,
    ytitle: str | None = None,
    title: str | None = None,
) -> go.Figure:
    figure.update_layout(
        title={
            "text": title if title else None,
            "y": 0.97,
            "x": 0.082,
            "xanchor": "center",
            "yanchor": "top",
            "font": {
                # "family": "Arial, sans-serif",  # Specify the font family here
                "size": 30,  # Specify the font size here
                "color": "white",  # Specify the font color here
            },
        },
        height=650,
        width=1450,
        font={"size": 20, "color": "white"},
        margin={"l": 150, "r": 30, "b": 0, "t": 0, "pad": 4},
        yaxis={
            "title": ytitle if ytitle else None,
            "tickmode": "array",
            "tickvals": list(range(-350, 351, 5)),
            "ticktext": [str(i) + "%" for i in range(-350, 351, 5)],
        },
        plot_bgcolor="#101A2E",
        paper_bgcolor="#101A2E",
        legend={
            "orientation": "h",
            "yanchor": "top",
            "y": 1.1,
            "xanchor": "center",
            "x": 0.5,
        },
    )
    figure.update_yaxes(
        showgrid=True, gridwidth=1, gridcolor="#293e68", zeroline=False
    )
    figure.update_xaxes(
        showgrid=True, gridwidth=1, gridcolor="#293e68", zeroline=False
    )
    figure = add_bs_logo(figure)
    if range_slider:
        figure = add_range_slider(figure, end_time)

    return figure


def bs_standard_smile_layout(
    figure: go.Figure,
    vol_range: tuple[float, float],
    strike_range: tuple[float, float],
    title: str | None = None,
) -> go.Figure:
    tickvals = [round(i / 100, 1) for i in range(0, 1000, 10)]
    figure.update_layout(
        title={
            "text": f"{title}" if title else None,
            "x": 0.52,
            "xanchor": "center",
            "yanchor": "top",
            "font": {
                # "family": "Arial, sans-serif",  # Specify the font family here
                "size": 20,  # Specify the font size here
                "color": "white",  # Specify the font color here
            },
        },
        yaxis={
            "title": "Implied Volatility",
            "tickmode": "array",
            "tickvals": tickvals,
            "ticktext": [f"{i*100:.0f}%" for i in tickvals],
            "range": vol_range,
            "showgrid": True,
            "gridwidth": 1,
            "gridcolor": "#293e68",
            "zeroline": False,
        },
        xaxis={
            "tickmode": "array",
            "tickvals": list(range(0, 80001, 1000)),
            "ticktext": ["$" + str(i) + "K" for i in range(0, 80, 1)],
            "range": strike_range,
            "showgrid": True,
            "gridwidth": 1,
            "gridcolor": "#293e68",
            "zeroline": False,
        },
        height=700,
        width=1000,
        font={"size": 20, "color": "white"},
        margin={"l": 150, "r": 70, "b": 50, "t": 60, "pad": 4},
        plot_bgcolor="#101A2E",
        paper_bgcolor="#101A2E",
        legend={
            "orientation": "h",
            "yanchor": "top",
            "y": 0.95,
            "xanchor": "center",
            "x": 0.50,
        },
    )

    figure.update_yaxes(
        showgrid=True, gridwidth=1, gridcolor="#293e68", zeroline=False
    )
    figure.update_xaxes(
        showgrid=True, gridwidth=1, gridcolor="#293e68", zeroline=False
    )
    figure = add_bs_logo(figure)

    return figure


def bs_flex_smile_layout(
    figure: go.Figure,
    y_axis_range: tuple[float, float],
    x_axis_range: tuple[float, float],
    domain: Literal["moneyness", "delta"] | None,
    chart_title: str | None = None,
    y_title: str | None = None,
    x_title: str | None = None,
    y_axis_tick_mult: float = 1.0,
    x_axis_tick_mult: float = 1.0,
    y_axis_ticks_suffix: str | None = "",
    x_axis_tick_suffix: str | None = "",
    x_axis_steps: int = 10,
    y_axis_steps: int = 10,
) -> go.Figure:
    def delta_tick_val(val: int | float) -> float:
        return_val: float | int = 0
        if val < 0.5:
            return_val = -val
        elif val > 0.5:
            return_val = 1 - val
        elif val == 0.5:
            return_val = val
        return round(return_val, 1)

    # Determine tickvals and ticktext for the volatility (y-axis)
    y_axis_tickvals = [
        i / 100
        for i in range(
            int(y_axis_range[0] * 100), int(y_axis_range[1] * 100), y_axis_steps
        )
    ]
    y_axis_ticktext = [
        f"{round(i * y_axis_tick_mult)}{y_axis_ticks_suffix}"
        for i in y_axis_tickvals
    ]

    if domain == "moneyness":
        x_axis_tickvals = [
            i / 100
            for i in range(
                int(x_axis_range[0] * 100),
                int(x_axis_range[1] * 100),
                x_axis_steps,
            )
        ]
        x_title = "Forward Moneyness"
    elif domain == "delta":
        x_axis_tickvals = [
            i / 100
            for i in range(
                int(x_axis_range[0] * 100),
                int(x_axis_range[1] * 100),
                x_axis_steps,
            )
        ]
        x_title = "Put Delta"
        x_axis_tick_suffix = ""
    else:
        x_axis_tickvals = [
            i / 100
            for i in range(
                int(x_axis_range[0] * 100),
                int(x_axis_range[1] * 100),
                x_axis_steps,
            )
        ]

    x_axis_ticktext = [
        f"{round(i * x_axis_tick_mult, 1)}{x_axis_tick_suffix}"
        for i in x_axis_tickvals
    ]

    figure.update_layout(
        title={
            "text": f"{chart_title}" if chart_title else None,
            "y": 0.9,
            "x": 0.52,
            "xanchor": "center",
            "yanchor": "top",
            "font": {
                "size": 20,
                "color": "white",
            },
        },
        yaxis={
            "title": y_title,
            "tickmode": "array",
            "tickvals": y_axis_tickvals,
            "ticktext": y_axis_ticktext,
            "range": y_axis_range,
            "showgrid": True,
            "gridwidth": 1,
            "gridcolor": "#293e68",
            "zeroline": False,
        },
        xaxis={
            "title": x_title,
            "tickmode": "array",
            "tickvals": x_axis_tickvals,
            "ticktext": x_axis_ticktext,
            "range": x_axis_range,
            "showgrid": True,
            "gridwidth": 1,
            "gridcolor": "#293e68",
            "zeroline": False,
        },
        height=700,
        width=1000,
        font={"size": 20, "color": "white"},
        margin={"l": 150, "r": 70, "b": 50, "t": 60, "pad": 4},
        plot_bgcolor="#101A2E",
        paper_bgcolor="#101A2E",
        legend={
            "orientation": "h",
            "yanchor": "top",
            "y": 0.95,
            "xanchor": "center",
            "x": 0.50,
        },
    )

    figure.update_yaxes(
        showgrid=True, gridwidth=1, gridcolor="#293e68", zeroline=False
    )
    figure.update_xaxes(
        showgrid=True, gridwidth=1, gridcolor="#293e68", zeroline=False
    )

    figure = add_bs_logo(figure)

    return figure


def bs_standard_term_structure_layout(
    figure: go.Figure,
    vol_range: tuple[float, float] | None,
    strike_range: tuple[float, float],
    title: str | None = None,
) -> go.Figure:
    figure.update_layout(
        title={
            "text": title if {title} else None,
            "y": 0.98,
            "x": 0.52,
            "xanchor": "center",
            "yanchor": "top",
            "font": {
                # "family": "Arial, sans-serif"
                "size": 20,  # Specify the font size here
                "color": "white",  # Specify the font color here
            },
        },
        yaxis={
            "title": "Implied Volatility",
            "tickmode": "array",
            "range": vol_range if vol_range else [],
            "showgrid": True,
            "gridwidth": 1,
            "gridcolor": "#293e68",
            "zeroline": False,
        },
        xaxis={
            "title": "Tenor (days)",
            "tickmode": "array",
            "range": strike_range,
            "showgrid": True,
            "gridwidth": 1,
            "gridcolor": "#293e68",
            "zeroline": False,
        },
        height=450,
        width=1450,
        font={"size": 20, "color": "white"},
        margin={
            "l": 150,  # left margin
            "r": 70,  # right margin
            "b": 50,  # bottom margin, increase this value to push the chart up
            "t": 50,  # top margin
            "pad": 4,  # Padding between the plotting area and the axis lines
        },
        plot_bgcolor="#101A2E",
        paper_bgcolor="#101A2E",
        legend={
            "orientation": "h",
            "yanchor": "top",
            "y": 1.10,
            "xanchor": "center",
            "x": 0.47,
        },
    )

    figure.update_yaxes(
        showgrid=True, gridwidth=1, gridcolor="#293e68", zeroline=False
    )
    figure.update_xaxes(
        showgrid=True, gridwidth=1, gridcolor="#293e68", zeroline=False
    )
    figure = add_bs_logo(figure)

    return figure


def add_bs_logo(fig: go.Figure) -> go.Figure:
    # Convert the PNG image to Base64
    with open(
        Path(__file__).parent.parent.resolve()
        / "assets"
        / "watermark"
        / "Block Scholes Full Logo cleaned.png",
        "rb",
    ) as image_file:
        encoded_string = base64.b64encode(image_file.read()).decode()

    # Using the Base64-encoded image in Plotly
    fig.add_layout_image(
        {
            "source": "data:image/png;base64," + encoded_string,
            "xref": "paper",
            "yref": "paper",
            "x": 0.5,
            "y": 0.5,
            "sizex": 0.4,
            "sizey": 0.4,
            "xanchor": "center",
            "yanchor": "middle",
            "opacity": 0.5,
        }
    )
    return fig


def add_range_slider(figure: go.Figure, end_time: int) -> go.Figure:
    figure.update_layout(
        xaxis={
            "rangeselector": {
                "buttons": [
                    {
                        "count": 1,
                        "label": "1m",
                        "step": "month",
                        "stepmode": "backward",
                    },
                    {
                        "count": 2,
                        "label": "2m",
                        "step": "month",
                        "stepmode": "backward",
                    },
                    {
                        "count": 1,
                        "label": "YTD",
                        "step": "year",
                        "stepmode": "todate",
                    },
                    {
                        "count": 1,
                        "label": "1y",
                        "step": "year",
                        "stepmode": "backward",
                    },
                    {"step": "all"},
                ],
                "font": {"size": 13, "color": "#E0E0E0"},
                "bgcolor": "#4B8BBE",
                "activecolor": "#FFA500",
                "bordercolor": "#25395D",
                "borderwidth": 1,
                "xanchor": "left",
                "y": 1.13,
                "yanchor": "top",
            },
            "rangeslider": {"visible": True},
            "type": "date",
            "range": [
                pd.to_datetime(end_time, unit="ns") - pd.Timedelta(30, "days"),
                pd.to_datetime(end_time, unit="ns"),
            ],
        }
    )
    return figure


def generate_xaxis_ticks(min_val: int, max_val: int, interval: int) -> Tick:
    # Generate tick values
    tickvals = list(range(min_val, max_val + interval, interval))

    # Generate tick texts with specific format
    ticktext = [f"${i / 1000}K" for i in tickvals]

    # Return the dictionary for xaxis configuration
    return {
        "title": "Strike",
        "tickmode": "array",
        "tickvals": tickvals,
        "ticktext": ticktext,
    }


def update_fig_layout(
    fig: go.Figure,
    chart_title: str,
    yaxis_title: str,
    tickvals: Sequence[int],
    ticktext: Sequence[str] | None = None,
    y_range: tuple[float | int, float | int] | None = None,
) -> go.Figure:
    bs_fig = bs_standard_timeseries_layout(
        figure=fig,
        title=chart_title,
        ytitle=yaxis_title,
        end_time=int(datetime.now(tz=UTC).timestamp()),
        range_slider=False,
    )
    if ticktext is None:
        # default as this is mainly used for vol related plots
        ticktext = [f"{abbreviate_number(val=i)}%" for i in tickvals]

    bs_fig.update_layout(
        yaxis={
            "tickmode": "array",
            "tickvals": tickvals,
            "ticktext": ticktext,
            **(
                {"range": [y_range[0], y_range[1]]}
                if y_range is not None
                else {}
            ),
        },
    )

    return bs_fig


def rgb_to_hex(rgb: tuple[float, float, float]) -> str:
    return f"#{int(rgb[0] * 255):02x}{int(rgb[1] * 255):02x}{int(rgb[2] * 255):02x}"


def _get_tick_interval(min_val: float, max_val: float, num_ticks: int) -> int:
    return math.floor((max_val - min_val) / num_ticks)


def bs_flex_timeseries_layout(
    figure: go.Figure,
    chart_title: str | None = None,
    y_title: str | None = None,
    y_tick_prefix: str | None = "",
    y_tick_suffix: str | None = "",
) -> go.Figure:

    figure.update_layout(
        title={
            "text": f"{chart_title}" if chart_title else None,
            "y": 0.9,
            "x": 0.52,
            "xanchor": "center",
            "yanchor": "top",
            "font": {
                "size": 20,
                "color": "white",
            },
        },
        yaxis={
            "title": y_title,
            "showgrid": True,
            "gridwidth": 1,
            "gridcolor": "#293e68",
            "zeroline": False,
            "ticksuffix": y_tick_suffix,
            "tickprefix": y_tick_prefix,
        },
        height=450,
        width=1450,
        font={"size": 20, "color": "white"},
        margin={"l": 150, "r": 70, "b": 50, "t": 60, "pad": 4},
        plot_bgcolor="#101A2E",
        paper_bgcolor="#101A2E",
        legend={
            "orientation": "h",
            "yanchor": "top",
            "y": 0.95,
            "xanchor": "center",
            "x": 0.50,
        },
    )

    figure.update_yaxes(
        showgrid=True,
        gridwidth=1,
        gridcolor="#293e68",
        zeroline=False,
        showticksuffix="all",
        showtickprefix="all",
    )
    figure.update_xaxes(
        showgrid=True, gridwidth=1, gridcolor="#293e68", zeroline=False
    )

    figure = add_bs_logo(figure)

    return figure


def _determine_scaling_factor(
    min_val: float, max_val: float, num_ticks: int
) -> float:
    """
    Determine the scaling factor for min_val and max_val such that the tick interval is at least 1,
    given a desired number of ticks.

    Args:
    - min_val (float): The minimum value.
    - max_val (float): The maximum value.
    - num_ticks (int): The desired number of ticks.

    Returns:
    - scaling_factor (float): The scaling factor to be applied.
    """
    range_val = max_val - min_val

    # If the range is zero, return 1 to avoid division by zero
    if range_val == 0:
        return 1

    # Calculate the initial tick interval
    tick_interval = range_val / num_ticks

    # Determine the scaling factor to make the tick interval at least 1
    scaling_factor = math.ceil(1 / tick_interval)

    # Return the adjusted scaling factor
    return scaling_factor


def create_custom_gradient(base_color: str, n_colors: int) -> list[str]:
    base_rgb = mc.to_rgb(base_color)
    gradient = sns.blend_palette([base_rgb, (0.5, 0.5, 0.5)], n_colors)
    return [rgb_to_hex(color) for color in gradient]


def generate_tickvals_and_text_for_aggregation_plots(
    min_val: int | float, max_val: int | float, num_ticks: int = 10
) -> tuple[NDArray[str], list[str]]:
    # Generate tick values
    tickvals = np.linspace(min_val, max_val, num_ticks)

    if min_val < 0 < max_val and 0 not in tickvals:
        tickvals = np.append(tickvals, 0)
        tickvals = np.sort(tickvals)

    # Generate tick text, removing negative signs
    ticktext = [
        "$" + abbreviate_number(val=val, use_absolute=True) for val in tickvals
    ]

    return tickvals, ticktext


def abbreviate_number(
    *,
    val: float,
    use_absolute: bool = False,
    y_scale: float | None = None,
    min_decimals: int = 2,
) -> str:
    """
    Abbreviates a number using K, M, B, or T for thousands, millions, billions, and trillions.
    The number of decimals is determined by y_scale if provided, ensuring a minimum of min_decimals.

    Args:
        val (float): The number to format.
        use_absolute (bool, optional):
            If True, format using the absolute value (i.e. without a negative sign).
            If False, preserve the original sign. Defaults to True.

    Returns:
        str: The abbreviated number.
    """

    if y_scale is not None:
        if y_scale <= 0:
            raise ValueError(f"`y_scale` must be positive, {y_scale}")
        if y_scale < 1:
            decimals = max(min_decimals, -math.floor(math.log10(y_scale)) + 1)
        else:
            decimals = min_decimals
    else:
        decimals = min_decimals

    # Always use the magnitude to determine the suffix
    magnitude = abs(val)

    suffix = ""
    num = magnitude
    if magnitude >= 1e12:
        num = magnitude / 1e12
        suffix = "T"
    elif magnitude >= 1e9:
        num = magnitude / 1e9
        suffix = "B"
    elif magnitude >= 1e6:
        num = magnitude / 1e6
        suffix = "M"
    elif magnitude >= 1e3:
        num = magnitude / 1e3
        suffix = "K"

    # If we are not using the absolute value and the original value is negative,
    # prepend a negative sign.
    if not use_absolute and val < 0:
        num = -num

    format_str = f"{{:.{decimals}f}}"
    return f"{format_str.format(num)}{suffix}"


def get_y_scale(num: float) -> float:
    return 10 ** math.floor(math.log10(abs(num)))


def generate_tickvals(
    min_val: float | int, max_val: float | int, num_ticks: int = 10
) -> np.ndarray:
    """
    Generate an array of evenly spaced tick values for an axis.

    If the range spans 0 (min_val < 0 < max_val) and a simple linear spacing
    between min_val and max_val does not naturally include 0, then force a tick at 0.
    The spacing d is computed based on the dominant side (the side farther from 0).
    The resulting grid need not include the original bounds exactly but guarantees that
    the original min and max are within the grid's extent.

    Args:
        min_val (float): The lower bound.
        max_val (float): The upper bound.
        num_ticks (int, optional): The target number of ticks on the dominant side plus one for zero.
                                   Defaults to 10.

    Returns:
        np.ndarray: An array of evenly spaced tick values.
    """
    # For ranges that don't span 0, return a simple linspace.
    if min_val >= 0 or max_val <= 0:
        return np.linspace(min_val, max_val, num_ticks)

    # Check if a linspace grid from min_val to max_val already includes 0 (within tolerance).
    ticks_lin = np.linspace(min_val, max_val, num_ticks)
    if np.any(np.isclose(ticks_lin, 0, atol=1e-8)):
        return ticks_lin

    # Determine the dominant side (the one with the larger absolute value).
    if abs(min_val) >= max_val:
        d = abs(min_val) / (num_ticks - 1)
    else:
        d = max_val / (num_ticks - 1)

    # Compute the number of ticks needed on each side so that:
    #   new_min = -n_neg * d  <= min_val
    #   new_max = n_pos * d   >= max_val
    n_neg = int(np.ceil(abs(min_val) / d))
    n_pos = int(np.ceil(max_val / d))

    # Generate the ticks: negative side (in ascending order) and positive side (including 0).
    neg_ticks = [-i * d for i in range(n_neg, 0, -1)]
    pos_ticks = [i * d for i in range(0, n_pos + 1)]

    ticks = np.array(neg_ticks + pos_ticks)
    # By construction, ticks[0] <= min_val and ticks[-1] >= max_val.
    return ticks


def expand_y_range(
    tickvals: list[float],
    min_val: float,
    max_val: float,
    y_scale: float | None = None,
    padding_fraction: float = 0.01,
    tick_spacing_multiplier: float = 1.5,  # How many tick intervals to use as padding
) -> tuple[float, float]:
    """
    Expand the y-axis range to include extra padding so that the border ticks are fully visible.

    The function adjusts the bounds as follows:
      - Lower bound:
          * If the tick corresponding to the minimum value is negative, the new minimum becomes more negative.
          * If it is positive, the new minimum is lowered.
      - Upper bound:
          * If the tick corresponding to the maximum value is positive, the new maximum becomes more positive.
          * If it is negative, the new maximum is raised.

    Args:
        tickvals (list[float]): A list of y-axis tick values.
        min_val (float): The current minimum tick value.
        max_val (float): The current maximum tick value.
        padding_fraction (float, optional): The fraction by which to expand the range (default is 0.01 for 1% padding).

    Returns:
        tuple[float, float]: The new (min, max) y-axis range with added padding.
    """
    if y_scale is not None:
        padding_fraction = min(y_scale, padding_fraction)

    tick_min = next(
        (t for t in reversed(tickvals) if t <= min_val),
        tickvals[0],
    )
    tick_max = next((t for t in tickvals if t >= max_val), tickvals[-1])

    # Check if we have a very narrow range (constant or nearly constant data)
    current_range = tick_max - tick_min
    if len(tickvals) > 1:
        tick_spacing = abs(tickvals[1] - tickvals[0])

        # If current range is smaller than 2 tick intervals, use spacing-based padding
        if current_range < tick_spacing * 2:
            padding = tick_spacing * tick_spacing_multiplier
            return tick_min - padding, tick_max + padding

    # Calculate new minimum
    if tick_min < 0:
        new_min = tick_min - abs(tick_min) * padding_fraction
    elif tick_min > 0:
        new_min = tick_min - tick_min * padding_fraction
    else:
        # When exactly zero, choose a small negative padding
        new_min = -padding_fraction

    # Calculate new maximum
    if tick_max > 0:
        new_max = tick_max + tick_max * padding_fraction
    elif tick_max < 0:
        new_max = tick_max + abs(tick_max) * padding_fraction
    else:
        # When exactly zero, choose a small positive padding
        new_max = padding_fraction

    return new_min, new_max
