import json
import pathlib
from typing import Any, cast

import pytest

from research_vol_dashboard.typings import DashboardEventArgs, GrabbedResult


@pytest.fixture
def event_args() -> DashboardEventArgs:
    return cast(DashboardEventArgs, load_fixture("timeseries/event_args.json"))


def load_fixture(filename: str) -> Any:
    """
    Loads a fixture from the fixtures folder.

    Args:
        filename (str): The name of the fixture file.

    Returns:
        Any: The loaded fixture data.
    """
    fixture_path = (
        pathlib.Path(__file__).parent.resolve() / "input_data" / filename
    )
    with open(fixture_path) as file:
        data = json.load(file)
    return data


@pytest.fixture
def mock_retrieve_data_timeseries() -> dict[str, GrabbedResult]:
    return {
        "v2timeseries": GrabbedResult(
            data=load_fixture("timeseries/timeseries.json"),
            instruments=[],
        )
    }


@pytest.fixture
def mock_event() -> Any:
    """
    Provides a mock event for the lambda_handler.

    """
    return load_fixture("timeseries/event.json")


@pytest.fixture
def mock_context() -> dict[str, Any]:
    return {}
